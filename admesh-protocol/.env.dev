# Development Environment Configuration
# Environment
ENV=development
DEBUG=true
LOG_LEVEL=DEBUG
PORT=8000

# Firebase Configuration
GOOGLE_APPLICATION_CREDENTIALS=./firebase/dev-serviceAccountKey.json

# API Configuration
SITE_URL=http://127.0.0.1:8000

# External Services
OPENAI_API_KEY=**************************************************************************************************************************************************
OPENROUTER_API_KEY=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
RESEND_API_KEY=re_EqBocs67_QFgdUbbUEkVvGeommYFp2wXQ

# Test IDs and Keys
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
NEXT_PUBLIC_AGENT_API_KEY=sk_test_IFTLcrkWf2Hx9GUfb6pNXwaMpJ4GryRw

# Stripe Configuration (Test)
STRIPE_SECRET_KEY=sk_test_51RouIpDt7GHb2NdVi6DmyyxkBOwDjB6zwcbLFZ33N1KkvijSgLhI5wwP0GYuCBggYmVCWtoFHNk8HnvslxqSvt0M00K7iWrcha
STRIPE_DOMAIN=http://localhost:3000
STRIPE_WEBHOOK_SECRET=whsec_240424b7da34a1ba9b2649f213da18818050b1a6296432b0935a976655cdff78

# Frontend URLs
FRONTEND_URL=http://localhost:3000
FRONTEND_URL_PROD=https://useadmesh.com

# Security
FERNET_SECRET=VlWiQCfcL7LW34MpCN9UCmWHU0F2cUQecgiQBtGeCTM=

# Feature Flags (Development)
ENABLE_ANALYTICS=true
ENABLE_RATE_LIMITING=false
ENABLE_CACHING=false
ENABLE_EMAIL_VERIFICATION=false
ENABLE_TRUST_SCORE_THROTTLING=false
