# Docker Test Environment - Production-like settings
ENV=production
DEBUG=false
LOG_LEVEL=INFO
PORT=8000

# Firebase Configuration (for Docker testing)
GOOGLE_APPLICATION_CREDENTIALS=/app/firebase/serviceAccountKey.json

# API Configuration
SITE_URL=http://localhost:8000

# External Services (using your actual keys for testing)
OPENAI_API_KEY=**************************************************************************************************************************************************
OPENROUTER_API_KEY=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
RESEND_API_KEY=re_EqBocs67_QFgdUbbUEkVvGeommYFp2wXQ

# Test IDs and Keys
NEXT_PUBLIC_AGENT_ID=ZAinjf9SuPYCk7u8r0ZEeKY2fV42
NEXT_PUBLIC_USER_ID=C8SMDERS3naFG2k103EDBacoTCy2
NEXT_PUBLIC_AGENT_API_KEY=sk_test_IFTLcrkWf2Hx9GUfb6pNXwaMpJ4GryRw

# Stripe Configuration (Test)
STRIPE_SECRET_KEY=sk_test_51RouIpDt7GHb2NdVi6DmyyxkBOwDjB6zwcbLFZ33N1KkvijSgLhI5wwP0GYuCBggYmVCWtoFHNk8HnvslxqSvt0M00K7iWrcha
STRIPE_DOMAIN=http://localhost:3000
STRIPE_WEBHOOK_SECRET=whsec_9fcb1b9eaf6db496cd90a0afec2ce995a8ac3915e2a312466e5a40f2f9beb614

# Frontend URLs
FRONTEND_URL=http://localhost:3000
FRONTEND_URL_PROD=https://useadmesh.com

# Feature Flags for Testing
ENABLE_ANALYTICS=true
ENABLE_RATE_LIMITING=false
ENABLE_CACHING=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TRUST_SCORE_THROTTLING=true

# Security
FERNET_SECRET=VlWiQCfcL7LW34MpCN9UCmWHU0F2cUQecgiQBtGeCTM=

# Network Configuration
HOST=0.0.0.0
