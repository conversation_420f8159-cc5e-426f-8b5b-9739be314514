from fastapi import APIRouter, Request, Depends, HTTPException
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any
import stripe
import os
import uuid
import time
import hashlib
from firebase_admin import firestore
from firebase.config import get_db
from auth.deps import require_role
import logging
from datetime import datetime, timedelta
import traceback

router = APIRouter()
db = get_db()

stripe.api_key = os.environ.get("STRIPE_SECRET_KEY")
DOMAIN = os.environ.get("STRIPE_DOMAIN")  # e.g. https://admesh.com

# Configure structured logging
logger = logging.getLogger("admesh.billing")
webhook_logger = logging.getLogger("admesh.billing.webhook")
payment_logger = logging.getLogger("admesh.billing.payment")
audit_logger = logging.getLogger("admesh.billing.audit")

# Ensure loggers are configured
for log in [logger, webhook_logger, payment_logger, audit_logger]:
    if not log.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        log.addHandler(handler)
        log.setLevel(logging.INFO)

async def create_audit_log(
    action: str,
    brand_id: str,
    transaction_id: str,
    details: Dict[str, Any],
    status: str = "success",
    error_message: str = None
):
    """
    Create comprehensive audit log for all payment operations.
    """
    audit_data = {
        "timestamp": firestore.SERVER_TIMESTAMP,
        "action": action,
        "brand_id": brand_id,
        "transaction_id": transaction_id,
        "status": status,
        "details": details,
        "created_at": datetime.now().isoformat()
    }

    if error_message:
        audit_data["error_message"] = sanitize_error_message(error_message)

    try:
        # Store audit log in Firestore
        audit_ref = db.collection("audit_logs").document()
        await audit_ref.set(audit_data)

        # Also log to application logger
        audit_logger.info(
            f"Audit log created: {action}",
            extra={
                "audit_id": audit_ref.id,
                "action": action,
                "brand_id": brand_id,
                "transaction_id": transaction_id,
                "status": status,
                "details": details
            }
        )
    except Exception as e:
        # If audit logging fails, log the error but don't fail the main operation
        audit_logger.error(
            "Failed to create audit log",
            extra={
                "action": action,
                "brand_id": brand_id,
                "transaction_id": transaction_id,
                "error": str(e)
            }
        )

class CheckoutRequest(BaseModel):
    # All monetary values must be integers in cents
    amount: int = Field(..., gt=0, le=1000000, description="Total amount in cents (including fees)")
    credit_amount: Optional[int] = Field(None, gt=0, le=1000000, description="Actual credit amount in cents (without fees)")

    @validator('amount')
    def validate_amount(cls, v):
        if v < 100:  # $1.00 minimum
            raise ValueError('Amount must be at least $1.00 (100 cents)')
        if v > 1000000:  # $10,000 maximum
            raise ValueError('Amount cannot exceed $10,000 (1,000,000 cents)')
        return v

    @validator('credit_amount')
    def validate_credit_amount(cls, v, values):
        if v is not None:
            if v < 100:  # $1.00 minimum
                raise ValueError('Credit amount must be at least $1.00 (100 cents)')
            if v > 1000000:  # $10,000 maximum
                raise ValueError('Credit amount cannot exceed $10,000 (1,000,000 cents)')
            # Credit amount should not exceed total amount
            if 'amount' in values and v > values['amount']:
                raise ValueError('Credit amount cannot exceed total amount')
        return v

# Rate limiting storage (in production, use Redis or similar)
rate_limit_storage = {}

def check_rate_limit(brand_id: str, max_requests: int = 5, window_minutes: int = 10) -> bool:
    """
    Check if the brand has exceeded the rate limit for payment requests.
    Returns True if within limit, False if exceeded.
    """
    now = datetime.now()
    window_start = now - timedelta(minutes=window_minutes)

    # Clean old entries
    if brand_id in rate_limit_storage:
        rate_limit_storage[brand_id] = [
            timestamp for timestamp in rate_limit_storage[brand_id]
            if timestamp > window_start
        ]
    else:
        rate_limit_storage[brand_id] = []

    # Check if limit exceeded
    if len(rate_limit_storage[brand_id]) >= max_requests:
        return False

    # Add current request
    rate_limit_storage[brand_id].append(now)
    return True

def sanitize_error_message(error_message: str) -> str:
    """
    Sanitize error messages to prevent information leakage.
    """
    # Remove sensitive information patterns
    sensitive_patterns = [
        r'sk_[a-zA-Z0-9_]+',  # Stripe secret keys
        r'pk_[a-zA-Z0-9_]+',  # Stripe public keys
        r'whsec_[a-zA-Z0-9_]+',  # Stripe webhook secrets
        r'firebase[a-zA-Z0-9_\-\.]+',  # Firebase credentials
        r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # Email addresses
    ]

    sanitized = error_message
    for pattern in sensitive_patterns:
        import re
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)

    return sanitized

@router.post("/create-checkout-session")
async def create_checkout_session(
    data: CheckoutRequest,
    request: Request,
    user=Depends(require_role("brand"))
):
    # Generate unique transaction ID for tracking
    transaction_id = str(uuid.uuid4())
    brand_id = user["uid"]
    client_ip = request.client.host if request.client else "unknown"

    # Check rate limiting
    if not check_rate_limit(brand_id):
        payment_logger.warning(
            "Payment session creation rate limited",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "client_ip": client_ip,
                "rate_limit_exceeded": True
            }
        )
        raise HTTPException(
            status_code=429,
            detail={
                "error": "RATE_LIMIT_EXCEEDED",
                "message": "Too many payment requests. Please wait before trying again.",
                "retry_after_minutes": 10
            }
        )

    # Log payment initiation (without sensitive data)
    payment_logger.info(
        "Payment session creation initiated",
        extra={
            "transaction_id": transaction_id,
            "brand_id": brand_id,
            "amount_cents": data.amount,
            "credit_amount_cents": data.credit_amount,
            "amount_dollars": data.amount / 100,
            "credit_amount_dollars": (data.credit_amount or data.amount) / 100,
            "client_ip": client_ip
        }
    )

    # Validate the amount (in cents)
    if data.amount < 100:  # 100 cents = $1.00
        payment_logger.warning(
            "Payment session creation failed: amount too low",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "amount_cents": data.amount,
                "minimum_required": 100
            }
        )
        raise HTTPException(
            status_code=400,
            detail={
                "error": "AMOUNT_TOO_LOW",
                "message": "Minimum amount is $1.00",
                "minimum_amount_cents": 100,
                "provided_amount_cents": data.amount
            }
        )

    # Validate maximum amount (e.g., $10,000)
    if data.amount > 1000000:  # 1,000,000 cents = $10,000
        payment_logger.warning(
            "Payment session creation failed: amount too high",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "amount_cents": data.amount,
                "maximum_allowed": 1000000
            }
        )
        raise HTTPException(
            status_code=400,
            detail={
                "error": "AMOUNT_TOO_HIGH",
                "message": "Maximum amount is $10,000",
                "maximum_amount_cents": 1000000,
                "provided_amount_cents": data.amount
            }
        )

    try:
        # Create Stripe checkout session
        session = stripe.checkout.Session.create(
            payment_method_types=["card"],
            line_items=[{
                "price_data": {
                    "currency": "usd",
                    "unit_amount": data.amount,  # Already in cents
                    "product_data": {
                        "name": "AdMesh Wallet Top-up",
                        "description": f"Add ${(data.credit_amount or data.amount) / 100:.2f} to your AdMesh wallet"
                    },
                },
                "quantity": 1,
            }],
            mode="payment",
            success_url=f"{DOMAIN}/dashboard/brand/billing?success=true&session_id={{CHECKOUT_SESSION_ID}}",
            cancel_url=f"{DOMAIN}/dashboard/brand/billing?canceled=true&session_id={{CHECKOUT_SESSION_ID}}",
            metadata={
                "brand_id": brand_id,
                "amount": str(data.credit_amount or data.amount),
                "transaction_id": transaction_id,
                "total_amount": str(data.amount),
                "credit_amount": str(data.credit_amount or data.amount)
            }
        )

        # Log successful session creation
        payment_logger.info(
            "Stripe checkout session created successfully",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "stripe_session_id": session.id,
                "amount_cents": data.amount,
                "credit_amount_cents": data.credit_amount or data.amount
            }
        )

        # Log session creation for audit
        audit_logger.info(
            "Checkout session created",
            extra={
                "action": "checkout_session_created",
                "brand_id": brand_id,
                "transaction_id": transaction_id,
                "stripe_session_id": session.id,
                "amount_cents": data.amount,
                "credit_amount_cents": data.credit_amount or data.amount,
                "amount_dollars": data.amount / 100,
                "credit_amount_dollars": (data.credit_amount or data.amount) / 100,
                "client_ip": client_ip,
                "user_agent": request.headers.get("user-agent", "unknown")
            }
        )

        # Create pending transaction record
        pending_tx_data = {
            "type": "credit",
            "category": "stripe_deposit_pending",
            "amount": data.credit_amount or data.amount,
            "status": "pending",
            "timestamp": firestore.SERVER_TIMESTAMP,
            "description": f"Pending payment - ${(data.credit_amount or data.amount)/100:.2f} to be added to wallet",
            "stripe_session_id": session.id,
            "transaction_id": transaction_id,
            "metadata": {
                "total_amount_cents": data.amount,
                "credit_amount_cents": data.credit_amount or data.amount,
                "payment_method": "stripe_checkout",
                "session_url": session.url
            }
        }

        try:
            _create_transaction_record(brand_id, pending_tx_data)
        except Exception as e:
            # Don't fail the checkout creation if transaction record fails
            payment_logger.warning(
                "Failed to create pending transaction record",
                extra={
                    "transaction_id": transaction_id,
                    "brand_id": brand_id,
                    "error": str(e)
                }
            )

        return {
            "session_url": session.url,
            "session_id": session.id,
            "transaction_id": transaction_id
        }

    except stripe.error.StripeError as e:
        # Log Stripe-specific errors (with full details for debugging)
        payment_logger.error(
            "Stripe error during session creation",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "stripe_error_type": type(e).__name__,
                "stripe_error_code": getattr(e, 'code', None),
                "stripe_error_message": sanitize_error_message(str(e)),
                "amount_cents": data.amount,
                "client_ip": client_ip
            }
        )

        # Return sanitized error to client
        error_message = "Payment service temporarily unavailable"
        if hasattr(e, 'user_message') and e.user_message:
            error_message = sanitize_error_message(e.user_message)

        raise HTTPException(
            status_code=500,
            detail={
                "error": "STRIPE_ERROR",
                "message": error_message,
                "transaction_id": transaction_id
            }
        )
    except Exception as e:
        # Log unexpected errors (with full details for debugging)
        payment_logger.error(
            "Unexpected error during session creation",
            extra={
                "transaction_id": transaction_id,
                "brand_id": brand_id,
                "error_type": type(e).__name__,
                "error_message": sanitize_error_message(str(e)),
                "traceback": traceback.format_exc(),
                "amount_cents": data.amount,
                "client_ip": client_ip
            }
        )

        # Return generic error to client
        raise HTTPException(
            status_code=500,
            detail={
                "error": "INTERNAL_ERROR",
                "message": "An unexpected error occurred. Please try again later.",
                "transaction_id": transaction_id
            }
        )

@router.post("/webhook")
async def stripe_webhook(request: Request):
    webhook_id = str(uuid.uuid4())

    try:
        payload = await request.body()
        sig_header = request.headers.get("stripe-signature")
        endpoint_secret = os.environ.get("STRIPE_WEBHOOK_SECRET")

        webhook_logger.info(
            "Webhook request received",
            extra={
                "webhook_id": webhook_id,
                "signature_present": bool(sig_header),
                "payload_size": len(payload) if payload else 0
            }
        )

        if not endpoint_secret:
            webhook_logger.error(
                "Webhook secret not configured",
                extra={"webhook_id": webhook_id}
            )
            raise HTTPException(status_code=500, detail="Webhook configuration error")

        try:
            event = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)

            webhook_logger.info(
                "Webhook event verified successfully",
                extra={
                    "webhook_id": webhook_id,
                    "event_type": event['type'],
                    "event_id": event.get('id'),
                    "livemode": event.get('livemode', False)
                }
            )

            # Log the full event response for debugging
            webhook_logger.info(
                "Full webhook event data",
                extra={
                    "webhook_id": webhook_id,
                    "event_type": event['type'],
                    "event_id": event.get('id'),
                    "full_event": event
                }
            )

        except stripe.error.SignatureVerificationError as e:
            webhook_logger.error(
                "Webhook signature verification failed",
                extra={
                    "webhook_id": webhook_id,
                    "error_message": str(e),
                    "signature_header": sig_header[:50] + "..." if sig_header and len(sig_header) > 50 else sig_header
                }
            )
            raise HTTPException(status_code=400, detail="Invalid webhook signature")

        # Handle different event types
        if event["type"] == "checkout.session.completed":
            session = event["data"]["object"]
            metadata = session.get("metadata", {})

            webhook_logger.info(
                "Processing checkout session completion",
                extra={
                    "webhook_id": webhook_id,
                    "session_id": session.get("id"),
                    "payment_status": session.get("payment_status"),
                    "amount_total": session.get("amount_total"),
                    "metadata": metadata
                }
            )

            # Handle regular one-time payment
            _handle_payment_checkout(session, webhook_id)

        elif event["type"] == "checkout.session.expired":
            session = event["data"]["object"]
            _handle_payment_failure(session, webhook_id, "expired", "Checkout session expired")

        elif event["type"] == "payment_intent.payment_failed":
            payment_intent = event["data"]["object"]
            _handle_payment_intent_failure(payment_intent, webhook_id, "payment_failed", "Payment failed")

        elif event["type"] == "charge.failed":
            charge = event["data"]["object"]
            _handle_charge_failure(charge, webhook_id, "charge_failed", "Charge failed")

        elif event["type"] == "payment_intent.succeeded":
            payment_intent = event["data"]["object"]

            webhook_logger.info(
                "Processing payment intent succeeded event",
                extra={
                    "webhook_id": webhook_id,
                    "payment_intent_id": payment_intent.get("id"),
                    "amount": payment_intent.get("amount"),
                    "currency": payment_intent.get("currency"),
                    "status": payment_intent.get("status"),
                    "metadata": payment_intent.get("metadata", {}),
                    "charges_count": len(payment_intent.get("charges", {}).get("data", []))
                }
            )

            _handle_payment_intent_succeeded(payment_intent, webhook_id)

        else:
            webhook_logger.info(
                "Webhook event type not handled",
                extra={
                    "webhook_id": webhook_id,
                    "event_type": event['type'],
                    "event_id": event.get('id')
                }
            )

        return {"status": "success", "webhook_id": webhook_id}

    except HTTPException:
        # Re-raise HTTP exceptions (they're already logged above)
        raise
    except Exception as e:
        webhook_logger.error(
            "Unexpected error processing webhook",
            extra={
                "webhook_id": webhook_id,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "traceback": traceback.format_exc()
            }
        )
        raise HTTPException(status_code=500, detail="Webhook processing error")

# Helper function to handle one-time payment checkout
def _handle_payment_checkout(session, webhook_id: str):
    """Handle successful one-time payment checkout"""
    session_id = session.get("id")
    metadata = session.get("metadata", {})
    payment_status = session.get("payment_status")
    amount_total = session.get("amount_total")

    payment_logger.info(
        "Processing payment checkout",
        extra={
            "webhook_id": webhook_id,
            "session_id": session_id,
            "payment_status": payment_status,
            "amount_total": amount_total,
            "metadata": metadata
        }
    )

    # Validate required metadata
    try:
        brand_id = metadata["brand_id"]
        amount = int(metadata["amount"])  # Credit amount in cents
        transaction_id = metadata.get("transaction_id")
        total_amount = int(metadata.get("total_amount", amount))  # Total amount charged
    except (KeyError, ValueError, TypeError) as e:
        payment_logger.error(
            "Invalid or missing metadata in webhook",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "metadata": metadata,
                "error": str(e)
            }
        )
        raise HTTPException(status_code=400, detail="Invalid metadata in webhook")

    # Validate payment was successful
    if payment_status != "paid":
        payment_logger.warning(
            "Payment not completed successfully",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "brand_id": brand_id,
                "payment_status": payment_status,
                "expected_status": "paid"
            }
        )
        return

    # Get the brand document
    brand_ref = db.collection("brands").document(brand_id)

    try:
        brand_doc = brand_ref.get()
    except Exception as e:
        payment_logger.error(
            "Failed to fetch brand document",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "brand_id": brand_id,
                "error": str(e)
            }
        )
        raise HTTPException(status_code=500, detail="Database error")

    if not brand_doc.exists:
        payment_logger.error(
            "Brand not found in database",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "brand_id": brand_id
            }
        )
        return

    # Find existing pending transaction before starting the atomic transaction
    try:
        # Query for pending transaction outside of the transaction
        wallet_tx_collection = db.collection("wallets").document(brand_id).collection("transactions")
        pending_tx_query = wallet_tx_collection.where("transaction_id", "==", transaction_id).where("status", "==", "pending")
        pending_tx_docs = list(pending_tx_query.stream())

        payment_tx_collection = db.collection("payments").document(brand_id).collection("transactions")
        payment_pending_query = payment_tx_collection.where("transaction_id", "==", transaction_id).where("status", "==", "pending")
        payment_pending_docs = list(payment_pending_query.stream())

        # Define the transaction function (synchronous for Firestore)
        @firestore.transactional
        def update_wallet_balance(transaction, brand_ref, credit_amount, pending_tx_docs, payment_pending_docs):
            # Get current brand data
            brand_doc = brand_ref.get(transaction=transaction)
            if not brand_doc.exists:
                payment_logger.error(
                    "Brand document not found during transaction",
                    extra={
                        "webhook_id": webhook_id,
                        "session_id": session_id,
                        "brand_id": brand_id
                    }
                )
                return None

            brand_data = brand_doc.to_dict()
            current_balance = brand_data.get("wallet_balance_cents", 0)
            new_balance = current_balance + credit_amount

            # Update brand wallet balance
            transaction.update(brand_ref, {
                "wallet_balance_cents": new_balance,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            # Get or create wallet document
            wallet_ref = db.collection("wallets").document(brand_id)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if wallet_doc.exists:
                wallet_data = wallet_doc.to_dict()
                current_available = wallet_data.get("total_available_balance", 0)
                new_available = current_available + credit_amount

                transaction.update(wallet_ref, {
                    "total_available_balance": new_available,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            else:
                # Create new wallet document
                wallet_data = {
                    "brand_id": brand_id,
                    "total_available_balance": credit_amount,
                    "total_promo_available_balance": 0,
                    "total_promo_balance_spent": 0,
                    "total_balance_spent": 0,
                    "total_budget_allocated": 0,
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                transaction.set(wallet_ref, wallet_data)
                new_available = credit_amount

            # Add transaction record to payments collection (legacy)
            payment_tx_data = {
                "type": "deposit",
                "amount": credit_amount,
                "timestamp": firestore.SERVER_TIMESTAMP,
                "description": f"Wallet top-up via Stripe (${credit_amount/100:.2f})",
                "status": "completed",
                "stripe_session_id": session_id,
                "transaction_id": transaction_id,
                "webhook_id": webhook_id,
                "total_charged": total_amount,
                "credit_received": credit_amount
            }

            payment_tx_ref = db.collection("payments").document(brand_id).collection("transactions").document()
            transaction.set(payment_tx_ref, payment_tx_data)

            # Update existing pending transaction to completed status using pre-queried documents
            if pending_tx_docs:
                # Update existing pending transaction
                pending_tx_ref = pending_tx_docs[0].reference
                transaction.update(pending_tx_ref, {
                    "status": "completed",
                    "balance_after": new_available,
                    "updated_at": firestore.SERVER_TIMESTAMP,
                    "webhook_id": webhook_id,
                    "metadata": {
                        "total_charged_cents": total_amount,
                        "credit_received_cents": credit_amount,
                        "stripe_fee_cents": total_amount - credit_amount,
                        "payment_method": "stripe_checkout"
                    }
                })

                # Also update in payments collection using pre-queried documents
                if payment_pending_docs:
                    payment_pending_ref = payment_pending_docs[0].reference
                    transaction.update(payment_pending_ref, {
                        "status": "completed",
                        "balance_after": new_available,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "webhook_id": webhook_id
                    })
            else:
                # Fallback: Create new completed transaction if pending not found
                wallet_tx_data = {
                    "type": "credit",
                    "category": "stripe_deposit",
                    "amount": credit_amount,
                    "status": "completed",
                    "timestamp": firestore.SERVER_TIMESTAMP,
                    "description": f"Stripe payment - ${credit_amount/100:.2f} added to wallet",
                    "stripe_session_id": session_id,
                    "transaction_id": transaction_id,
                    "webhook_id": webhook_id,
                    "balance_after": new_available,
                    "metadata": {
                        "total_charged_cents": total_amount,
                        "credit_received_cents": credit_amount,
                        "stripe_fee_cents": total_amount - credit_amount,
                        "payment_method": "stripe_checkout"
                    }
                }

                wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions").document()
                transaction.set(wallet_tx_ref, wallet_tx_data)

            return new_balance

        # Execute the atomic transaction
        transaction = db.transaction()
        new_balance = update_wallet_balance(transaction, brand_ref, amount, pending_tx_docs, payment_pending_docs)

        if new_balance is None:
            payment_logger.error(
                "Failed to update wallet balance - transaction returned None",
                extra={
                    "webhook_id": webhook_id,
                    "session_id": session_id,
                    "brand_id": brand_id,
                    "amount": amount
                }
            )
            return

        payment_logger.info(
            "Wallet balance updated successfully",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "brand_id": brand_id,
                "previous_balance_cents": new_balance - amount,
                "credit_added_cents": amount,
                "new_balance_cents": new_balance,
                "total_charged_cents": total_amount,
                "transaction_id": transaction_id
            }
        )

        # Create audit log for successful payment processing
        # Note: We'll skip audit logging in synchronous context for now
        # TODO: Implement proper async audit logging
        audit_logger.info(
            "Payment processed successfully",
            extra={
                "webhook_id": webhook_id,
                "stripe_session_id": session_id,
                "brand_id": brand_id,
                "transaction_id": transaction_id,
                "payment_status": payment_status,
                "amount_total_stripe": amount_total,
                "previous_balance_cents": new_balance - amount,
                "credit_added_cents": amount,
                "new_balance_cents": new_balance,
                "total_charged_cents": total_amount,
                "stripe_fee_cents": total_amount - amount,
                "payment_method": "stripe_checkout"
            }
        )

    except Exception as e:
        error_message = str(e)
        payment_logger.error(
            "Critical error updating wallet balance",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "brand_id": brand_id,
                "amount": amount,
                "error_type": type(e).__name__,
                "error_message": sanitize_error_message(error_message),
                "traceback": traceback.format_exc()
            }
        )

        # Log failed payment processing
        audit_logger.error(
            "Payment processing failed",
            extra={
                "webhook_id": webhook_id,
                "stripe_session_id": session_id,
                "brand_id": brand_id,
                "transaction_id": transaction_id,
                "amount": amount,
                "total_amount": total_amount,
                "error_type": type(e).__name__,
                "error_message": sanitize_error_message(error_message)
            }
        )

        raise HTTPException(status_code=500, detail="Failed to update wallet balance")

    payment_logger.info(
        "Payment processing completed successfully",
        extra={
            "webhook_id": webhook_id,
            "session_id": session_id,
            "brand_id": brand_id,
            "amount_credited_cents": amount,
            "total_charged_cents": total_amount,
            "transaction_id": transaction_id
        }
    )


def _create_transaction_record(brand_id: str, transaction_data: dict, webhook_id: str = None):
    """Create a transaction record in the database"""
    try:
        # Add to wallet transactions collection
        wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions").document()
        wallet_tx_ref.set(transaction_data)

        # Also add to payments transactions collection for audit
        payment_tx_ref = db.collection("payments").document(brand_id).collection("transactions").document()
        payment_tx_ref.set(transaction_data)

        payment_logger.info(
            "Transaction record created successfully",
            extra={
                "webhook_id": webhook_id,
                "brand_id": brand_id,
                "transaction_type": transaction_data.get("type"),
                "transaction_status": transaction_data.get("status"),
                "amount": transaction_data.get("amount")
            }
        )

    except Exception as e:
        payment_logger.error(
            "Failed to create transaction record",
            extra={
                "webhook_id": webhook_id,
                "brand_id": brand_id,
                "error": str(e),
                "transaction_data": transaction_data
            }
        )


def _handle_payment_failure(session, webhook_id: str, failure_type: str, failure_reason: str):
    """Handle failed payment from checkout session"""
    session_id = session.get("id")
    metadata = session.get("metadata", {})

    webhook_logger.info(
        f"Processing payment failure: {failure_type}",
        extra={
            "webhook_id": webhook_id,
            "session_id": session_id,
            "failure_type": failure_type,
            "failure_reason": failure_reason,
            "metadata": metadata
        }
    )

    try:
        brand_id = metadata["brand_id"]
        amount = int(metadata.get("amount", 0))
        transaction_id = metadata.get("transaction_id")
        total_amount = int(metadata.get("total_amount", amount))
    except (KeyError, ValueError, TypeError) as e:
        payment_logger.error(
            "Invalid or missing metadata in failed payment webhook",
            extra={
                "webhook_id": webhook_id,
                "session_id": session_id,
                "metadata": metadata,
                "error": str(e)
            }
        )
        return

    # Create failed transaction record
    failed_tx_data = {
        "type": "debit",  # Failed attempt to add funds
        "category": "stripe_deposit_failed",
        "amount": 0,  # No money was actually added
        "status": "failed",
        "failure_type": failure_type,
        "failure_reason": failure_reason,
        "timestamp": firestore.SERVER_TIMESTAMP,
        "description": f"Failed to add ${amount/100:.2f} to wallet - {failure_reason}",
        "stripe_session_id": session_id,
        "transaction_id": transaction_id,
        "webhook_id": webhook_id,
        "metadata": {
            "attempted_amount_cents": amount,
            "total_attempted_cents": total_amount,
            "payment_method": "stripe_checkout",
            "failure_details": {
                "type": failure_type,
                "reason": failure_reason,
                "session_status": session.get("status"),
                "payment_status": session.get("payment_status")
            }
        }
    }

    _create_transaction_record(brand_id, failed_tx_data, webhook_id)

    # Log failed payment for audit
    audit_logger.error(
        "Payment failed",
        extra={
            "webhook_id": webhook_id,
            "stripe_session_id": session_id,
            "brand_id": brand_id,
            "transaction_id": transaction_id,
            "failure_type": failure_type,
            "failure_reason": failure_reason,
            "attempted_amount_cents": amount,
            "total_attempted_cents": total_amount
        }
    )


def _handle_payment_intent_failure(payment_intent, webhook_id: str, failure_type: str, failure_reason: str):
    """Handle failed payment intent"""
    payment_intent_id = payment_intent.get("id")
    metadata = payment_intent.get("metadata", {})

    webhook_logger.info(
        f"Processing payment intent failure: {failure_type}",
        extra={
            "webhook_id": webhook_id,
            "payment_intent_id": payment_intent_id,
            "failure_type": failure_type,
            "failure_reason": failure_reason,
            "metadata": metadata
        }
    )

    try:
        brand_id = metadata["brand_id"]
        amount = int(metadata.get("amount", 0))
        transaction_id = metadata.get("transaction_id")
        total_amount = int(metadata.get("total_amount", amount))
    except (KeyError, ValueError, TypeError) as e:
        payment_logger.error(
            "Invalid or missing metadata in failed payment intent webhook",
            extra={
                "webhook_id": webhook_id,
                "payment_intent_id": payment_intent_id,
                "metadata": metadata,
                "error": str(e)
            }
        )
        return

    # Create failed transaction record
    failed_tx_data = {
        "type": "debit",
        "category": "stripe_payment_intent_failed",
        "amount": 0,
        "status": "failed",
        "failure_type": failure_type,
        "failure_reason": failure_reason,
        "timestamp": firestore.SERVER_TIMESTAMP,
        "description": f"Payment intent failed for ${amount/100:.2f} - {failure_reason}",
        "stripe_payment_intent_id": payment_intent_id,
        "transaction_id": transaction_id,
        "webhook_id": webhook_id,
        "metadata": {
            "attempted_amount_cents": amount,
            "total_attempted_cents": total_amount,
            "payment_method": "stripe_checkout",
            "failure_details": {
                "type": failure_type,
                "reason": failure_reason,
                "last_payment_error": payment_intent.get("last_payment_error"),
                "status": payment_intent.get("status")
            }
        }
    }

    _create_transaction_record(brand_id, failed_tx_data, webhook_id)


def _handle_payment_intent_succeeded(payment_intent, webhook_id: str):
    """Handle successful payment intent - add funds to wallet"""
    payment_intent_id = payment_intent.get("id")
    amount = payment_intent.get("amount", 0)  # Amount in cents
    charges = payment_intent.get("charges", {}).get("data", [])
    charge_id = charges[0].get("id") if charges else None
    metadata = payment_intent.get("metadata", {})

    webhook_logger.info(
        "Processing payment intent success",
        extra={
            "webhook_id": webhook_id,
            "payment_intent_id": payment_intent_id,
            "charge_id": charge_id,
            "amount": amount,
            "metadata": metadata
        }
    )

    # Extract brand_id and transaction_id from metadata
    try:
        brand_id = metadata.get("brand_id")
        transaction_id = metadata.get("transaction_id")

        if not brand_id:
            payment_logger.error(
                "Missing brand_id in payment intent metadata",
                extra={
                    "webhook_id": webhook_id,
                    "payment_intent_id": payment_intent_id,
                    "metadata": metadata
                }
            )
            return

        if not transaction_id:
            payment_logger.error(
                "Missing transaction_id in payment intent metadata",
                extra={
                    "webhook_id": webhook_id,
                    "payment_intent_id": payment_intent_id,
                    "metadata": metadata
                }
            )
            return

    except (KeyError, ValueError, TypeError) as e:
        payment_logger.error(
            "Invalid or missing metadata in payment intent webhook",
            extra={
                "webhook_id": webhook_id,
                "payment_intent_id": payment_intent_id,
                "metadata": metadata,
                "error": str(e)
            }
        )
        return

    # Find existing pending transaction before starting the atomic transaction
    try:
        # Query for pending transaction outside of the transaction
        wallet_tx_collection = db.collection("wallets").document(brand_id).collection("transactions")
        pending_tx_query = wallet_tx_collection.where("transaction_id", "==", transaction_id).where("status", "==", "pending")
        pending_tx_docs = list(pending_tx_query.stream())

        payment_tx_collection = db.collection("payments").document(brand_id).collection("transactions")
        payment_pending_query = payment_tx_collection.where("transaction_id", "==", transaction_id).where("status", "==", "pending")
        payment_pending_docs = list(payment_pending_query.stream())

        # Define the transaction function for wallet balance update
        @firestore.transactional
        def update_wallet_balance_for_payment_intent(transaction, brand_ref, credit_amount, pending_tx_docs, payment_pending_docs):
            # Get current brand data
            brand_doc = brand_ref.get(transaction=transaction)
            if not brand_doc.exists:
                payment_logger.error(
                    "Brand document not found during payment intent transaction",
                    extra={
                        "webhook_id": webhook_id,
                        "payment_intent_id": payment_intent_id,
                        "brand_id": brand_id
                    }
                )
                return None

            brand_data = brand_doc.to_dict()
            current_balance = brand_data.get("wallet_balance_cents", 0)
            new_balance = current_balance + credit_amount

            # Update brand wallet balance
            transaction.update(brand_ref, {
                "wallet_balance_cents": new_balance,
                "updated_at": firestore.SERVER_TIMESTAMP
            })

            # Get or create wallet document
            wallet_ref = db.collection("wallets").document(brand_id)
            wallet_doc = wallet_ref.get(transaction=transaction)

            if wallet_doc.exists:
                wallet_data = wallet_doc.to_dict()
                current_available = wallet_data.get("total_available_balance", 0)
                new_available = current_available + credit_amount

                transaction.update(wallet_ref, {
                    "total_available_balance": new_available,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
            else:
                # Create new wallet document
                wallet_data = {
                    "brand_id": brand_id,
                    "total_available_balance": credit_amount,
                    "total_promo_available_balance": 0,
                    "total_promo_balance_spent": 0,
                    "total_balance_spent": 0,
                    "total_budget_allocated": 0,
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                transaction.set(wallet_ref, wallet_data)
                new_available = credit_amount

            # Update existing pending transaction to completed status using pre-queried documents
            if pending_tx_docs:
                # Update existing pending transaction
                pending_tx_ref = pending_tx_docs[0].reference
                transaction.update(pending_tx_ref, {
                    "status": "completed",
                    "balance_after": new_available,
                    "updated_at": firestore.SERVER_TIMESTAMP,
                    "webhook_id": webhook_id,
                    "stripe_payment_intent_id": payment_intent_id,
                    "stripe_charge_id": charge_id,
                    "metadata": {
                        "payment_intent_id": payment_intent_id,
                        "charge_id": charge_id,
                        "payment_method": "stripe_payment_intent"
                    }
                })

                # Also update in payments collection using pre-queried documents
                if payment_pending_docs:
                    payment_pending_ref = payment_pending_docs[0].reference
                    transaction.update(payment_pending_ref, {
                        "status": "completed",
                        "balance_after": new_available,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "webhook_id": webhook_id,
                        "stripe_payment_intent_id": payment_intent_id,
                        "stripe_charge_id": charge_id
                    })
            else:
                # Fallback: Create new completed transaction if pending not found
                wallet_tx_data = {
                    "type": "credit",
                    "category": "stripe_payment_intent",
                    "amount": credit_amount,
                    "status": "completed",
                    "timestamp": firestore.SERVER_TIMESTAMP,
                    "description": f"Payment intent success - ${credit_amount/100:.2f} added to wallet",
                    "stripe_payment_intent_id": payment_intent_id,
                    "stripe_charge_id": charge_id,
                    "transaction_id": transaction_id,
                    "webhook_id": webhook_id,
                    "balance_after": new_available,
                    "metadata": {
                        "payment_intent_id": payment_intent_id,
                        "charge_id": charge_id,
                        "payment_method": "stripe_payment_intent"
                    }
                }

                wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions").document()
                transaction.set(wallet_tx_ref, wallet_tx_data)

            return new_available

        # Execute the atomic transaction
        brand_ref = db.collection("brands").document(brand_id)
        transaction = db.transaction()
        new_balance = update_wallet_balance_for_payment_intent(transaction, brand_ref, amount, pending_tx_docs, payment_pending_docs)

        if new_balance is not None:
            payment_logger.info(
                "Payment intent processed successfully",
                extra={
                    "webhook_id": webhook_id,
                    "payment_intent_id": payment_intent_id,
                    "charge_id": charge_id,
                    "brand_id": brand_id,
                    "amount_credited_cents": amount,
                    "transaction_id": transaction_id,
                    "new_wallet_balance": new_balance
                }
            )

            audit_logger.info(
                "Payment intent success processed",
                extra={
                    "webhook_id": webhook_id,
                    "payment_intent_id": payment_intent_id,
                    "charge_id": charge_id,
                    "brand_id": brand_id,
                    "amount_credited_cents": amount,
                    "transaction_id": transaction_id
                }
            )

    except Exception as e:
        payment_logger.error(
            "Critical error processing payment intent success",
            extra={
                "webhook_id": webhook_id,
                "payment_intent_id": payment_intent_id,
                "charge_id": charge_id,
                "brand_id": brand_id,
                "error": str(e),
                "traceback": traceback.format_exc()
            }
        )

        audit_logger.error(
            "Payment intent processing failed",
            extra={
                "webhook_id": webhook_id,
                "payment_intent_id": payment_intent_id,
                "charge_id": charge_id,
                "brand_id": brand_id,
                "error": str(e)
            }
        )
        raise


def _handle_charge_failure(charge, webhook_id: str, failure_type: str, failure_reason: str):
    """Handle failed charge"""
    charge_id = charge.get("id")
    metadata = charge.get("metadata", {})

    webhook_logger.info(
        f"Processing charge failure: {failure_type}",
        extra={
            "webhook_id": webhook_id,
            "charge_id": charge_id,
            "failure_type": failure_type,
            "failure_reason": failure_reason,
            "metadata": metadata
        }
    )

    try:
        brand_id = metadata["brand_id"]
        amount = int(metadata.get("amount", 0))
        transaction_id = metadata.get("transaction_id")
        total_amount = int(metadata.get("total_amount", amount))
    except (KeyError, ValueError, TypeError) as e:
        payment_logger.error(
            "Invalid or missing metadata in failed charge webhook",
            extra={
                "webhook_id": webhook_id,
                "charge_id": charge_id,
                "metadata": metadata,
                "error": str(e)
            }
        )
        return

    # Create failed transaction record
    failed_tx_data = {
        "type": "debit",
        "category": "stripe_charge_failed",
        "amount": 0,
        "status": "failed",
        "failure_type": failure_type,
        "failure_reason": failure_reason,
        "timestamp": firestore.SERVER_TIMESTAMP,
        "description": f"Charge failed for ${amount/100:.2f} - {failure_reason}",
        "stripe_charge_id": charge_id,
        "transaction_id": transaction_id,
        "webhook_id": webhook_id,
        "metadata": {
            "attempted_amount_cents": amount,
            "total_attempted_cents": total_amount,
            "payment_method": "stripe_checkout",
            "failure_details": {
                "type": failure_type,
                "reason": failure_reason,
                "failure_code": charge.get("failure_code"),
                "failure_message": charge.get("failure_message"),
                "status": charge.get("status")
            }
        }
    }

    _create_transaction_record(brand_id, failed_tx_data, webhook_id)


class Transaction(BaseModel):
    id: str
    type: str
    amount: int
    timestamp: Dict[str, int]
    description: str
    status: str

@router.get("/wallet")
def get_wallet(user=Depends(require_role("brand"))) -> Dict[str, Any]:
    """Get wallet information including balance and transaction history."""
    brand_id = user["uid"]

    # Get wallet data from wallets collection
    wallet_ref = db.collection("wallets").document(brand_id)
    wallet_doc = wallet_ref.get()

    if not wallet_doc.exists:
        # If no wallet document exists yet, create a new one
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(status_code=404, detail="Brand not found")

        # Create the wallet document with default values
        wallet_data = {
            "brand_id": brand_id,
            "total_available_balance": 0.0,
            "total_promo_available_balance": 0.0,
            "total_promo_balance_spent": 0.0,
            "total_balance_spent": 0.0,
            "total_budget_allocated": 0.0,
            "created_at": firestore.SERVER_TIMESTAMP,
            "updated_at": firestore.SERVER_TIMESTAMP
        }
        wallet_ref.set(wallet_data)
        wallet_data = wallet_data  # Use the data we just created
    else:
        wallet_data = wallet_doc.to_dict()

    return {
        "wallet_balance": wallet_data.get("total_available_balance", 0),  # For backward compatibility
        "total_spent": wallet_data.get("total_balance_spent", 0),  # For backward compatibility
        "current_total_budget": wallet_data.get("total_budget_allocated", 0),  # For backward compatibility
        "total_available_balance": wallet_data.get("total_available_balance", 0),
        "total_promo_available_balance": wallet_data.get("total_promo_available_balance", 0),
        "total_promo_balance_spent": wallet_data.get("total_promo_balance_spent", 0),
        "total_balance_spent": wallet_data.get("total_balance_spent", 0),
        "total_budget_allocated": wallet_data.get("total_budget_allocated", 0)
    }


@router.get("/transactions")
def get_transactions(user=Depends(require_role("brand"))) -> Dict[str, List[Dict[str, Any]]]:
    """Get transaction history for the authenticated brand."""
    brand_id = user["uid"]
    transactions: List[Dict[str, Any]] = []

    try:
        # Get transactions from the wallet transactions collection
        wallet_tx_ref = db.collection("wallets").document(brand_id).collection("transactions")
        wallet_tx_query = wallet_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING)
        wallet_tx_docs = wallet_tx_query.stream()

        for doc in wallet_tx_docs:
            tx = doc.to_dict()
            tx["id"] = doc.id
            tx["source"] = "wallet"  # Add source to distinguish from payment transactions

            # Convert timestamp to a serializable format
            if "timestamp" in tx and tx["timestamp"] is not None:
                if hasattr(tx["timestamp"], "seconds"):
                    tx["timestamp"] = {
                        "seconds": tx["timestamp"].seconds,
                        "nanoseconds": tx["timestamp"].nanoseconds
                    }

            transactions.append(tx)

        # Get transactions from the payments collection
        payment_tx_ref = db.collection("payments").document(brand_id).collection("transactions")
        payment_tx_query = payment_tx_ref.order_by("timestamp", direction=firestore.Query.DESCENDING)
        payment_tx_docs = payment_tx_query.stream()

        for doc in payment_tx_docs:
            tx = doc.to_dict()
            tx["id"] = f"payment_{doc.id}"  # Add prefix to avoid ID conflicts
            tx["source"] = "payments"  # Add source to distinguish from wallet transactions

            # Convert timestamp to a serializable format
            if "timestamp" in tx and tx["timestamp"] is not None:
                if hasattr(tx["timestamp"], "seconds"):
                    tx["timestamp"] = {
                        "seconds": tx["timestamp"].seconds,
                        "nanoseconds": tx["timestamp"].nanoseconds
                    }

            transactions.append(tx)

        # Sort all transactions by timestamp (newest first)
        transactions.sort(key=lambda x: (
            x.get("timestamp", {}).get("seconds", 0) if isinstance(x.get("timestamp"), dict) else 0
        ), reverse=True)

        return {"transactions": transactions}

    except Exception as e:
        logger.error(f"Error fetching transactions: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))