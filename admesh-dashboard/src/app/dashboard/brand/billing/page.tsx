"use client";

import { useEffect, useState, useCallback } from "react";
import { useAuth } from "@/hooks/use-auth";
import { centsToDollars, formatCurrency as formatCurrencyUtil } from "@/lib/utils";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  CreditCard,
  CircleDollarSign,
} from "lucide-react";
import DashboardFooter from "@/components/DashboardFooter";
import AddFundsDialog from "@/components/AddFundsDialog";
import { toast } from "sonner";

export default function BillingPage() {
  const { user } = useAuth();
  const [walletData, setWalletData] = useState({
    total_available_balance: 0,
    total_promo_available_balance: 0,
    total_promo_balance_spent: 0,
    total_balance_spent: 0,
    total_budget_allocated: 0
  });
  const [transactions, setTransactions] = useState<
    { id: string; type: string; amount: number; timestamp: Date | null; offer_title?: string; description?: string; reference_id?: string; reference_type?: string; category?: string; balance_after?: number; source?: string; status?: string; failure_type?: string; failure_reason?: string; stripe_session_id?: string; stripe_payment_intent_id?: string; stripe_charge_id?: string; transaction_id?: string }[]
  >([]);
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch billing data
  const fetchBillingData = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    setError(null);

    try {
      const token = await user.getIdToken();

      const [walletRes, txRes] = await Promise.all([
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/wallet`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
        fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/billing/transactions`, {
          headers: { Authorization: `Bearer ${token}` },
        }),
      ]);

      if (!walletRes.ok) {
        throw new Error(`Failed to fetch wallet data: ${walletRes.status}`);
      }

      if (!txRes.ok) {
        throw new Error(`Failed to fetch transaction data: ${txRes.status}`);
      }

      const walletData = await walletRes.json();
      const txData = await txRes.json();

      // Define types for different timestamp formats
      type FirestoreTimestamp = { seconds: number; nanoseconds?: number };
      type AlternativeTimestamp = { _seconds: number };
      type TimestampFormat = string | FirestoreTimestamp | AlternativeTimestamp;

      // Type guard functions
      const isFirestoreTimestamp = (timestamp: unknown): timestamp is FirestoreTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && 'seconds' in timestamp;

      const isAlternativeTimestamp = (timestamp: unknown): timestamp is AlternativeTimestamp =>
        typeof timestamp === 'object' && timestamp !== null && '_seconds' in timestamp;

      const normalizedTx = txData.transactions?.map((tx: { id: string; type: string; amount: number; timestamp: TimestampFormat; offer_title?: string; description?: string; category?: string; reference_id?: string; reference_type?: string; balance_after?: number; source?: string; status?: string; failure_type?: string; failure_reason?: string; stripe_session_id?: string; stripe_payment_intent_id?: string; stripe_charge_id?: string; transaction_id?: string }) => {
        // Handle different timestamp formats
        let parsedTimestamp = null;

        if (tx.timestamp) {
          if (typeof tx.timestamp === "string") {
            // ISO string format
            parsedTimestamp = new Date(tx.timestamp);
          } else if (typeof tx.timestamp === "object") {
            if (isFirestoreTimestamp(tx.timestamp)) {
              // Firestore timestamp format with seconds
              const seconds = typeof tx.timestamp.seconds === "number"
                ? tx.timestamp.seconds
                : parseInt(tx.timestamp.seconds);

              const nanoseconds = tx.timestamp.nanoseconds
                ? (typeof tx.timestamp.nanoseconds === "number"
                  ? tx.timestamp.nanoseconds
                  : parseInt(tx.timestamp.nanoseconds)) / 1000000
                : 0;

              parsedTimestamp = new Date(seconds * 1000 + nanoseconds);
            } else if (isAlternativeTimestamp(tx.timestamp)) {
              // Alternative Firestore format
              parsedTimestamp = new Date(tx.timestamp._seconds * 1000);
            }
          }
        }

        return {
          ...tx,
          timestamp: parsedTimestamp
        };
      });

      // Set all the data from the wallet collection
      setWalletData({
        total_available_balance: walletData.total_available_balance ?? 0,
        total_promo_available_balance: walletData.total_promo_available_balance ?? 0,
        total_promo_balance_spent: walletData.total_promo_balance_spent ?? 0,
        total_balance_spent: walletData.total_balance_spent ?? 0,
        total_budget_allocated: walletData.total_budget_allocated ?? 0
      });
      setTransactions(normalizedTx ?? []);
    } catch (err) {
      console.error("🔥 Error fetching billing data:", err);
      setError(err instanceof Error ? err.message : 'Failed to load billing data');
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Check for success parameter in URL (from Stripe redirect)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');
    const sessionId = urlParams.get('session_id');

    if (success === 'true') {
      toast.success('Payment successful!', {
        description: 'Your wallet has been updated with the new funds.',
        duration: 5000,
      });

      // Log successful payment
      console.log('✅ Payment completed successfully', { sessionId });

      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);

      // Refresh billing data to show updated balance
      fetchBillingData();
    } else if (canceled === 'true') {
      toast.error('Payment canceled', {
        description: 'Your payment was canceled. No charges were made.',
        duration: 4000,
      });

      // Log canceled payment
      console.log('❌ Payment canceled by user', { sessionId });

      // Remove the query parameters from the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [fetchBillingData]);

  // Fetch billing data when user changes or after successful payment
  useEffect(() => {
    if (!user) return;
    fetchBillingData();
  }, [user, fetchBillingData]);



  // Format currency values
  // All monetary values are stored in cents in the database
  const formatCurrency = (value: number) => {
    // Convert from cents to dollars first, then format
    const dollars = centsToDollars(value);
    return formatCurrencyUtil(dollars);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-xl font-semibold">Billing</h1>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={fetchBillingData}
            disabled={isLoading}
            className="rounded-full"
            title="Refresh"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={isLoading ? "animate-spin" : ""}
            >
              <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
              <path d="M3 3v5h5" />
              <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
              <path d="M16 21h5v-5" />
            </svg>
          </Button>
          <Button variant="outline" onClick={() => setShowAddFunds(true)}>
            <CreditCard className="w-4 h-4 mr-2" />
            Add Funds
          </Button>
        </div>
      </div>

      <p className="text-muted-foreground text-sm">
        Track your offer budgets and spending activity.
      </p>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          <p>{error}</p>
          <Button
            variant="link"
            className="text-red-700 p-0 h-auto text-sm"
            onClick={fetchBillingData}
          >
            Try again
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Available Balance</CardTitle>
            <CreditCard className="w-5 h-5 text-purple-500" />
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Available balance is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_available_balance)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Total Spent</CardTitle>
            <CircleDollarSign className="w-5 h-5 text-red-500" />
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Total spent is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_balance_spent)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex justify-between items-center pb-2">
            <CardTitle className="text-sm text-muted-foreground">Budget Allocated</CardTitle>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-blue-500">
              <rect width="20" height="14" x="2" y="5" rx="2" />
              <line x1="2" x2="22" y1="10" y2="10" />
            </svg>
          </CardHeader>
          <CardContent className="text-2xl font-bold">
            {/* Budget allocated is stored in cents in the database, formatCurrency converts to dollars */}
            {formatCurrency(walletData.total_budget_allocated)}
          </CardContent>
        </Card>
      </div>



      <Card>
        <CardHeader>
          <CardTitle className="text-base font-semibold text-gray-900">Transaction History</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]" role="status">
                <span className="!absolute !-m-px !h-px !w-px !overflow-hidden !whitespace-nowrap !border-0 !p-0 ![clip:rect(0,0,0,0)]">Loading...</span>
              </div>
              <p className="mt-2 text-sm text-muted-foreground">Loading transactions...</p>
            </div>
          ) : transactions.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              <p>No transactions yet</p>
              <p className="text-sm mt-1">Add funds to your wallet to get started</p>
            </div>
          ) : (
            <ul className="divide-y">
              {transactions.map((tx) => (
                <li key={tx.id} className="p-4 text-sm hover:bg-gray-50">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center">
                      {/* Legacy transaction types */}
                      {tx.type === "add_funds" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Added funds</span>
                        </>
                      )}
                      {tx.type === "recharge_offer" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"/></svg>
                          </span>
                          <span>Recharged &quot;{tx.offer_title || 'Offer'}&quot;</span>
                        </>
                      )}
                      {tx.type === "spend" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Spent on campaign</span>
                        </>
                      )}

                      {/* New transaction types */}
                      {tx.type === "credit" && tx.category === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit added"}</span>
                        </>
                      )}
                      {tx.type === "debit" && tx.category === "budget_allocation" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2" /><line x1="2" x2="22" y1="10" y2="10" /></svg>
                          </span>
                          <span>
                            {tx.description || "Budget allocated"}
                            {tx.offer_title && <> for &quot;{tx.offer_title}&quot;</>}
                          </span>
                        </>
                      )}
                      {tx.type === "promo_credit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/></svg>
                          </span>
                          <span>{tx.description || "Promo credit"}</span>
                        </>
                      )}
                      {(tx.type === "budget_refund" || tx.category === "budget_refund" || (tx.type === "credit" && tx.category === "budget_refund")) && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M3 12h18m-9-9v18"/></svg>
                          </span>
                          <span>
                            {tx.description || "Budget refunded"}
                          </span>
                        </>
                      )}

                      {/* Stripe deposit transactions */}
                      {tx.type === "credit" && tx.category === "stripe_deposit" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>
                          </span>
                          <span>Added funds</span>
                          {tx.status === "completed" && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              ✓ Completed
                            </span>
                          )}
                        </>
                      )}
                      {tx.type === "credit" && tx.category === "stripe_deposit_pending" && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-yellow-100 text-yellow-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 6v6l4 2"/></svg>
                          </span>
                          <span>Adding funds</span>
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                            ⏳ Pending
                          </span>
                        </>
                      )}
                      {(tx.category === "stripe_deposit_failed" || tx.category === "stripe_payment_intent_failed" || tx.category === "stripe_charge_failed") && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-red-100 text-red-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><line x1="15" y1="9" x2="9" y2="15"/><line x1="9" y1="9" x2="15" y2="15"/></svg>
                          </span>
                          <span>Failed to add funds</span>
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                            ✗ Failed
                          </span>
                          {tx.failure_reason && (
                            <span className="ml-2 text-xs text-gray-500">
                              ({tx.failure_reason})
                            </span>
                          )}
                        </>
                      )}

                      {/* Fallback for any unhandled transaction types */}
                      {!(tx.type === "add_funds" ||
                         tx.type === "recharge_offer" ||
                         tx.type === "spend" ||
                         (tx.type === "credit" && tx.category === "promo_credit") ||
                         (tx.type === "debit" && tx.category === "budget_allocation") ||
                         tx.type === "promo_credit" ||
                         tx.type === "budget_refund" ||
                         tx.category === "budget_refund" ||
                         (tx.type === "credit" && tx.category === "budget_refund") ||
                         (tx.type === "credit" && tx.category === "stripe_deposit") ||
                         (tx.type === "credit" && tx.category === "stripe_deposit_pending") ||
                         tx.category === "stripe_deposit_failed" ||
                         tx.category === "stripe_payment_intent_failed" ||
                         tx.category === "stripe_charge_failed") && (
                        <>
                          <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 text-gray-800 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M12 8v4M12 16h.01"/></svg>
                          </span>
                          <span>
                            {tx.description || `${tx.type || 'Unknown'} ${tx.category ? `(${tx.category})` : ''}`}
                          </span>
                        </>
                      )}
                    </span>
                    <span className={`font-medium text-right ${
                      tx.status === "failed" ? "text-red-600" :
                      tx.status === "pending" ? "text-yellow-600" :
                      tx.type === "credit" ? "text-green-600" :
                      tx.type === "debit" ? "text-red-600" :
                      "text-gray-900"
                    }`}>
                      {/* Transaction amounts are stored in cents in the database, formatCurrency converts to dollars */}
                      {tx.status === "failed" ? "±" :
                       tx.status === "pending" ? "⏳" :
                       tx.type === "credit" ? "+" :
                       tx.type === "debit" ? "-" : ""}{formatCurrency(tx.amount || 0)}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 ml-8">
                    {tx.timestamp
                      ? tx.timestamp.toLocaleString(undefined, {
                          dateStyle: "medium",
                          timeStyle: "short",
                        })
                      : "Just now"}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      <AddFundsDialog
        open={showAddFunds}
        onClose={() => setShowAddFunds(false)}
        onSuccess={() => {
          setShowAddFunds(false);
          fetchBillingData(); // Refresh data after successful payment
        }}
      />

      {/* Dashboard Footer */}
      <DashboardFooter />
    </div>
  );
}
